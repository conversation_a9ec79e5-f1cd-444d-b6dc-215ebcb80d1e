/**
 * Module pour la gestion des favoris
 */
import API from '../api.js';

const FavoritesManager = {
    /**
     * Initialise le gestionnaire de favoris
     */
    init() {
        // Éléments DOM pour le dashboard
        this.favoritesContainer = document.querySelector('.favorites-container');
        if (this.favoritesContainer) {
            this.initFavoritesActions();
        }

        // Éléments DOM pour la page de détails du livre
        this.bookDetailsPage = document.querySelector('.book-details');
        if (this.bookDetailsPage) {
            this.initBookDetailsPage();
        }
    },

    /**
     * Initialise les actions sur les favoris dans le dashboard
     */
    initFavoritesActions() {
        // Boutons pour retirer un livre des favoris
        const removeButtons = document.querySelectorAll('.remove-favorite');
        removeButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                const bookId = button.dataset.bookId;
                await this.removeFavorite(bookId);
            });
        });

        // Initialiser les filtres et la recherche
        this.initFilters();
    },

    /**
     * Initialise les filtres et la recherche pour les favoris
     */
    initFilters() {
        const searchInput = document.getElementById('search-favorites');
        const searchBtn = document.getElementById('search-favorites-btn');
        const sortSelect = document.getElementById('sort-favorites');
        const applyBtn = document.getElementById('apply-favorites-filters');

        if (searchBtn && applyBtn) {
            // Recherche au clic sur le bouton
            searchBtn.addEventListener('click', () => {
                this.applyFilters();
            });

            // Recherche en appuyant sur Entrée
            searchInput?.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilters();
                }
            });

            // Appliquer les filtres au clic sur le bouton
            applyBtn.addEventListener('click', () => {
                this.applyFilters();
            });

            // Appliquer les filtres au changement de tri
            sortSelect?.addEventListener('change', () => {
                this.applyFilters();
            });
        }
    },

    /**
     * Applique les filtres et la recherche aux favoris
     */
    async applyFilters() {
        const searchInput = document.getElementById('search-favorites');
        const sortSelect = document.getElementById('sort-favorites');

        if (!searchInput || !sortSelect || !this.favoritesContainer) return;

        const search = searchInput.value.trim();
        const sort = sortSelect.value;

        try {
            // Afficher un indicateur de chargement
            this.favoritesContainer.innerHTML = `
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i> Chargement...
                </div>
            `;

            // Récupérer les favoris filtrés
            const response = await API.getFavorites(search, sort);
            const favorites = response.data;

            // Mettre à jour l'affichage
            if (favorites && favorites.length > 0) {
                this.favoritesContainer.innerHTML = `
                    <div class="filter-result-message">${favorites.length} livre(s) trouvé(s)</div>
                    <div class="books-grid">
                        ${favorites.map(book => this.generateBookCardHTML(book)).join('')}
                    </div>
                `;

                // Réinitialiser les événements
                this.initFavoritesActions();
            } else {
                this.favoritesContainer.innerHTML = `
                    <div class="empty-state">
                        <p>Aucun livre ne correspond à votre recherche.</p>
                        <button class="btn btn-secondary reset-filters" id="reset-favorites-filters">Réinitialiser les filtres</button>
                    </div>
                `;

                // Ajouter un événement pour réinitialiser les filtres
                const resetBtn = document.getElementById('reset-favorites-filters');
                if (resetBtn) {
                    resetBtn.addEventListener('click', () => {
                        searchInput.value = '';
                        sortSelect.value = 'title_asc';
                        this.applyFilters();
                    });
                }
            }
        } catch (error) {
            console.error('Erreur lors de l\'application des filtres:', error);
            this.favoritesContainer.innerHTML = `
                <div class="error-message">
                    <p>Une erreur est survenue lors du chargement des favoris.</p>
                    <button class="btn btn-secondary" id="retry-favorites">Réessayer</button>
                </div>
            `;

            // Ajouter un événement pour réessayer
            const retryBtn = document.getElementById('retry-favorites');
            if (retryBtn) {
                retryBtn.addEventListener('click', () => {
                    this.applyFilters();
                });
            }
        }
    },

    /**
     * Génère le HTML pour une carte de livre
     * @param {Object} book - Objet livre
     * @returns {string} - HTML de la carte
     */
    generateBookCardHTML(book) {
        return `
            <div class="book-card">
                <a href="/books/${book.id}" class="book-link">
                    <div class="book-cover">
                        <img src="${book.cover_image_url || 'https://via.placeholder.com/300x450?text=Nookli'}" alt="Couverture de ${book.title}">
                    </div>
                    <div class="book-info">
                        <h4>${book.title}</h4>
                        <p class="book-author">${book.author}</p>
                    </div>
                </a>
                <div class="book-actions">
                    <button class="btn btn-text remove-favorite" data-book-id="${book.id}">
                        <i class="fas fa-heart-broken"></i> Retirer des favoris
                    </button>
                    <button class="btn btn-text add-to-list-btn" data-book-id="${book.id}">
                        <i class="fas fa-list"></i> Ajouter à une liste
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * Initialise les actions sur la page de détails du livre
     */
    initBookDetailsPage() {
        const bookId = this.getBookIdFromUrl();
        if (!bookId) return;

        // Bouton pour ajouter/retirer des favoris
        const favoriteBtn = document.getElementById('favoriteBtn');
        if (favoriteBtn) {
            // Vérifier si l'utilisateur est connecté avant d'appeler l'API
            if (this.isUserLoggedIn()) {
                // Vérifier si le livre est déjà dans les favoris
                this.checkIfFavorite(bookId).then(isFavorite => {
                    this.updateFavoriteButton(favoriteBtn, isFavorite);
                }).catch(error => {
                    console.error('Erreur lors de la vérification des favoris:', error);
                    // En cas d'erreur, afficher le bouton par défaut
                    this.updateFavoriteButton(favoriteBtn, false);
                });
            }

            favoriteBtn.addEventListener('click', async () => {
                const isFavorite = favoriteBtn.classList.contains('active');
                if (isFavorite) {
                    await this.removeFavorite(bookId);
                } else {
                    await this.addFavorite(bookId);
                }
                this.updateFavoriteButton(favoriteBtn, !isFavorite);
            });
        }
    },

    /**
     * Vérifie si l'utilisateur est connecté
     * @returns {boolean} - true si l'utilisateur est connecté, false sinon
     */
    isUserLoggedIn() {
        // Vérifier la présence d'éléments qui indiquent que l'utilisateur est connecté
        return document.querySelector('#favoriteBtn') !== null &&
               !document.querySelector('.book-actions-guest');
    },

    /**
     * Récupère l'ID du livre depuis l'URL
     * @returns {number|null} - ID du livre ou null
     */
    getBookIdFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/books\/(\d+)/);
        return match ? parseInt(match[1]) : null;
    },

    /**
     * Vérifie si un livre est dans les favoris
     * @param {number} bookId - ID du livre
     * @returns {Promise<boolean>} - true si le livre est dans les favoris, false sinon
     */
    async checkIfFavorite(bookId) {
        try {
            const response = await API.getFavorites();
            const favorites = response.data;
            return favorites.some(book => book.id === parseInt(bookId));
        } catch (error) {
            console.error('Erreur lors de la vérification des favoris:', error);
            return false;
        }
    },

    /**
     * Met à jour l'apparence du bouton favori
     * @param {HTMLElement} button - Bouton à mettre à jour
     * @param {boolean} isFavorite - true si le livre est dans les favoris, false sinon
     */
    updateFavoriteButton(button, isFavorite) {
        if (isFavorite) {
            button.classList.add('active');
            button.innerHTML = '<i class="fas fa-heart"></i> Retirer des favoris';
        } else {
            button.classList.remove('active');
            button.innerHTML = '<i class="far fa-heart"></i> Ajouter aux favoris';
        }
    },

    /**
     * Ajoute un livre aux favoris
     * @param {number} bookId - ID du livre
     */
    async addFavorite(bookId) {
        try {
            await API.addFavorite(bookId);
        } catch (error) {
            alert(`Erreur: ${error.message}`);
        }
    },

    /**
     * Retire un livre des favoris
     * @param {number} bookId - ID du livre
     */
    async removeFavorite(bookId) {
        try {
            await API.removeFavorite(bookId);

            // Si nous sommes sur la page des favoris, supprimer l'élément du DOM
            if (this.favoritesContainer) {
                const bookCard = document.querySelector(`.book-card [data-book-id="${bookId}"]`).closest('.book-card');
                if (bookCard) {
                    bookCard.remove();

                    // Vérifier s'il reste des favoris
                    const remainingCards = this.favoritesContainer.querySelectorAll('.book-card');
                    if (remainingCards.length === 0) {
                        this.favoritesContainer.innerHTML = `
                            <div class="empty-state">
                                <p>Vous n'avez pas encore de livres favoris.</p>
                                <a href="/books" class="btn btn-secondary">Explorer le catalogue</a>
                            </div>
                        `;
                    }
                }
            }
        } catch (error) {
            alert(`Erreur: ${error.message}`);
        }
    }
};

export default FavoritesManager;
